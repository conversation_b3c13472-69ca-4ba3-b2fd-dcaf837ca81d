<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参考文献插入功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .editor-content {
            border: 1px solid #ccc;
            padding: 20px;
            min-height: 400px;
            background: white;
            margin: 20px 0;
        }
        .test-buttons {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>参考文献插入功能测试</h1>
    
    <div class="test-buttons">
        <button onclick="initializeTestDocument()">初始化测试文档</button>
        <button onclick="testReferenceInsertion()">测试参考文献插入</button>
        <button onclick="clearConsole()">清空控制台</button>
    </div>

    <div class="editor-content" id="editor-content">
        <p>这是一个测试文档，用于验证参考文献插入功能。</p>
        <p>文档中已经包含一些现有的参考文献：</p>
        
        <h2>参考文献</h2>
        <p>[1] 张三, & 李四. (2020). 人工智能在教育中的应用研究. *计算机科学*, 47(3), 25-32.</p>
        <p>[2] 王五, 赵六, & 孙七. (2021). 机器学习算法优化方法综述. *软件学报*, 32(4), 112-128.</p>
        <p>[4] 陈八. (2022). 深度学习在自然语言处理中的应用. *中文信息学报*, 36(2), 45-58.</p>
    </div>

    <div class="console-output" id="console-output">
        控制台输出将显示在这里...
    </div>

    <script type="module">
        // 模拟编辑器对象和相关函数
        let mockEditor = null;
        let consoleOutput = document.getElementById('console-output');

        // 重写 console.log 以显示在页面上
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        // 模拟 TipTap 编辑器结构
        function createMockEditor() {
            const content = document.getElementById('editor-content').innerHTML;
            
            return {
                state: {
                    doc: {
                        nodesBetween: function(from, to, callback) {
                            // 模拟文档节点遍历
                            const lines = content.split('\n').filter(line => line.trim());
                            lines.forEach((line, index) => {
                                const textContent = line.replace(/<[^>]*>/g, '').trim();
                                if (textContent) {
                                    callback({
                                        type: { name: 'paragraph' },
                                        textContent: textContent
                                    }, index * 10);
                                }
                            });
                        },
                        content: {
                            size: 1000
                        }
                    }
                },
                chain: () => ({
                    focus: () => ({
                        insertContentAt: (pos, content) => {
                            console.log(`模拟插入内容到位置 ${pos}: ${JSON.stringify(content)}`);
                            return { run: () => true };
                        }
                    })
                })
            };
        }

        // 模拟参考文献工具函数
        function extractReferenceRegion(editor) {
            console.log('🔍 模拟检测参考文献区域...');
            return {
                found: true,
                from: 100,
                to: 500
            };
        }

        window.initializeTestDocument = function() {
            mockEditor = createMockEditor();
            console.log('✅ 测试文档已初始化');
            console.log('📄 模拟编辑器已创建');
        };

        window.testReferenceInsertion = function() {
            if (!mockEditor) {
                console.log('❌ 请先初始化测试文档');
                return;
            }

            console.log('\n=== 开始测试参考文献插入功能 ===');
            
            // 测试参数
            const annotationNumber = 3;
            const content = '张秀娟, & 胡建国. (2016). 扩招背景下家庭对教育平等影响的演变. *云南师范大学学报(哲学社会科学版), 46*(6), 114–121.';
            
            console.log(`📝 测试插入编号 [${annotationNumber}] 的参考文献`);
            console.log(`📄 内容: "${content}"`);
            
            // 模拟调用我们的函数
            testInsertReferenceForAnnotation(mockEditor, annotationNumber, content);
        };

        window.clearConsole = function() {
            consoleOutput.textContent = '';
        };

        // 简化版的参考文献插入测试函数
        function testInsertReferenceForAnnotation(editor, annotationNumber, content) {
            try {
                console.log('\n=== 📋 插入前的参考文献状态 ===');
                
                // 模拟现有参考文献检测
                const existingReferences = [
                    { number: 1, text: '[1] 张三, & 李四. (2020). 人工智能在教育中的应用研究. *计算机科学*, 47(3), 25-32.', position: 150 },
                    { number: 2, text: '[2] 王五, 赵六, & 孙七. (2021). 机器学习算法优化方法综述. *软件学报*, 32(4), 112-128.', position: 200 },
                    { number: 4, text: '[4] 陈八. (2022). 深度学习在自然语言处理中的应用. *中文信息学报*, 36(2), 45-58.', position: 300 }
                ];

                console.log(`📊 总计: ${existingReferences.length} 个参考文献`);
                console.log('🎨 检测到的格式模式: "[数字] content"');
                
                console.log('\n📄 完整参考文献内容:');
                console.log('='.repeat(80));
                
                existingReferences.forEach((ref, index) => {
                    console.log(`\n${index + 1}. 参考文献 [${ref.number}] (位置: ${ref.position})`);
                    console.log(`   完整内容: "${ref.text}"`);
                    
                    // 格式分析
                    const match = ref.text.match(/^(\[(\d+)\])(\s*)(.+)/);
                    if (match) {
                        const numberPart = match[1];
                        const spacing = match[3];
                        const content = match[4];
                        console.log(`   格式分析: 编号="${numberPart}" 间距="${spacing}" (${spacing.length}字符) 内容="${content.substring(0, 50)}${content.length > 50 ? '...' : ''}"`);
                        console.log(`   格式一致性: ✅ 符合 预期格式`);
                    }
                });
                
                console.log('\n' + '='.repeat(80));
                
                // 位置计算
                console.log('\n🧮 计算插入位置...');
                console.log(`🎯 目标编号: [${annotationNumber}]`);
                
                const targetPositionIndex = annotationNumber - 1;
                console.log(`📍 目标位置索引: ${targetPositionIndex}`);
                
                let insertPosition;
                if (targetPositionIndex >= existingReferences.length) {
                    insertPosition = existingReferences.length > 0 ? existingReferences[existingReferences.length - 1].position + 50 : 250;
                    console.log(`   原因: 目标索引(${targetPositionIndex}) >= 现有数量(${existingReferences.length})`);
                    console.log(`   决策: 插入到末尾，位置 ${insertPosition}`);
                } else {
                    insertPosition = existingReferences[targetPositionIndex].position;
                    console.log(`   原因: 目标索引(${targetPositionIndex}) < 现有数量(${existingReferences.length})`);
                    console.log(`   决策: 插入到现有位置 ${insertPosition}`);
                }
                
                // 格式化内容
                console.log('\n✨ 格式化参考文献条目:');
                const formattedContent = `[${annotationNumber}] ${content}`;
                console.log(`   编号: [${annotationNumber}]`);
                console.log(`   使用间距: " " (1字符)`);
                console.log(`   原始内容: "${content}"`);
                console.log(`   格式化结果: "${formattedContent}"`);
                
                // 插入验证
                console.log('\n📝 即将插入的完整参考文献内容:');
                console.log(`   编号: [${annotationNumber}]`);
                console.log(`   原始内容: "${content}"`);
                console.log(`   格式化后: "${formattedContent}"`);
                console.log(`   使用模板: 自定义内容`);
                
                // 模拟插入
                console.log('\n🚀 执行插入操作...');
                const success = editor.chain().focus().insertContentAt(insertPosition, {
                    type: 'paragraph',
                    content: [{ type: 'text', text: formattedContent }]
                }).run();
                
                if (success) {
                    console.log('✅ 参考文献条目插入成功');
                    
                    // 模拟插入后状态
                    console.log('\n=== 📋 插入后的参考文献状态 ===');
                    const afterReferences = [...existingReferences];
                    afterReferences.splice(targetPositionIndex, 0, {
                        number: annotationNumber,
                        text: formattedContent,
                        position: insertPosition
                    });
                    
                    console.log(`📊 插入后参考文献总数: ${afterReferences.length}`);
                    console.log(`📈 新增参考文献数量: 1`);
                    
                    console.log('\n📄 更新后的完整参考文献内容:');
                    console.log('='.repeat(80));
                    
                    afterReferences.forEach((ref, index) => {
                        const isNew = ref.number === annotationNumber;
                        console.log(`\n${index + 1}. 参考文献 [${ref.number}] (位置: ${ref.position}) ${isNew ? '🆕 新插入' : ''}`);
                        console.log(`   完整内容: "${ref.text}"`);
                    });
                    
                    console.log('\n' + '='.repeat(80));
                    console.log('=== 参考文献插入完成 ===');
                } else {
                    console.log('❌ 参考文献条目插入失败');
                }
                
            } catch (error) {
                console.error('❌ 测试过程中发生错误:', error);
            }
        }

        // 页面加载完成后自动初始化
        window.addEventListener('load', function() {
            console.log('🎉 参考文献插入功能测试页面已加载');
            console.log('📝 请点击"初始化测试文档"开始测试');
        });
    </script>
</body>
</html>
